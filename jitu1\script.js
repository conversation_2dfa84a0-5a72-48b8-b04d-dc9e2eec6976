// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
        });
    }
    
    // Close mobile menu when clicking on a link
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
        });
    });
});

// Smooth scrolling for anchor links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Navbar background change on scroll
window.addEventListener('scroll', function() {
    const navbar = document.querySelector('.navbar');
    if (window.scrollY > 100) {
        navbar.style.background = 'rgba(30, 60, 114, 0.95)';
        navbar.style.backdropFilter = 'blur(10px)';
    } else {
        navbar.style.background = 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)';
        navbar.style.backdropFilter = 'none';
    }
});

// Animate elements on scroll
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.feature-card, .service-card');
    
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Form validation (for contact forms)
function validateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.style.borderColor = '#ff6b6b';
            isValid = false;
        } else {
            input.style.borderColor = '#ddd';
        }
    });
    
    return isValid;
}

// Package tracking functionality
function trackPackage() {
    const trackingNumber = document.getElementById('trackingNumber');
    const trackingResult = document.getElementById('trackingResult');

    if (!trackingNumber || !trackingNumber.value.trim()) {
        alert('Please enter a tracking number');
        return;
    }

    // Show loading
    showLoading(trackingResult);
    trackingResult.style.display = 'block';

    // Simulate API call delay
    setTimeout(() => {
        const trackingNum = trackingNumber.value.trim();
        let trackingData;

        // Different demo data based on tracking number
        switch(trackingNum) {
            case 'YE123456789':
                trackingData = {
                    status: 'delivered',
                    timeline: [
                        { status: 'completed', icon: 'fas fa-box', title: 'Package Picked Up', time: '2024-01-15 10:30 AM', location: 'Mumbai, Maharashtra' },
                        { status: 'completed', icon: 'fas fa-cogs', title: 'Processing at Facility', time: '2024-01-15 2:45 PM', location: 'Mumbai Sorting Center' },
                        { status: 'completed', icon: 'fas fa-truck', title: 'In Transit', time: '2024-01-16 8:00 AM', location: 'Delhi Hub' },
                        { status: 'completed', icon: 'fas fa-shipping-fast', title: 'Out for Delivery', time: '2024-01-17 9:00 AM', location: 'Delhi Local Office' },
                        { status: 'completed', icon: 'fas fa-check-circle', title: 'Delivered', time: '2024-01-17 3:30 PM', location: 'Delivered to Recipient' }
                    ]
                };
                break;
            case 'YE987654321':
                trackingData = {
                    status: 'transit',
                    timeline: [
                        { status: 'completed', icon: 'fas fa-box', title: 'Package Picked Up', time: '2024-01-18 11:00 AM', location: 'Pune, Maharashtra' },
                        { status: 'completed', icon: 'fas fa-cogs', title: 'Processing at Facility', time: '2024-01-18 4:15 PM', location: 'Pune Sorting Center' },
                        { status: 'active', icon: 'fas fa-truck', title: 'In Transit', time: '2024-01-19 7:30 AM', location: 'En route to Bangalore' },
                        { status: 'pending', icon: 'fas fa-shipping-fast', title: 'Out for Delivery', time: 'Expected: Tomorrow 10:00 AM', location: 'Bangalore Local Office' },
                        { status: 'pending', icon: 'fas fa-home', title: 'Delivered', time: 'Expected: Tomorrow 6:00 PM', location: 'Final Destination' }
                    ]
                };
                break;
            case 'YE456789123':
                trackingData = {
                    status: 'out-for-delivery',
                    timeline: [
                        { status: 'completed', icon: 'fas fa-box', title: 'Package Picked Up', time: '2024-01-19 9:15 AM', location: 'Chennai, Tamil Nadu' },
                        { status: 'completed', icon: 'fas fa-cogs', title: 'Processing at Facility', time: '2024-01-19 1:30 PM', location: 'Chennai Sorting Center' },
                        { status: 'completed', icon: 'fas fa-truck', title: 'In Transit', time: '2024-01-19 6:45 PM', location: 'Local Distribution Center' },
                        { status: 'active', icon: 'fas fa-shipping-fast', title: 'Out for Delivery', time: 'Today 8:00 AM', location: 'With Delivery Agent' },
                        { status: 'pending', icon: 'fas fa-home', title: 'Delivered', time: 'Expected: Today by 7:00 PM', location: 'Final Destination' }
                    ]
                };
                break;
            default:
                trackingData = {
                    status: 'not-found',
                    message: 'Tracking number not found. Please check your tracking number and try again.'
                };
        }

        if (trackingData.status === 'not-found') {
            trackingResult.innerHTML = `
                <div class="tracking-info">
                    <div class="tracking-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>Tracking Number Not Found</h3>
                        <p>${trackingData.message}</p>
                        <div class="error-help">
                            <p>Please ensure:</p>
                            <ul>
                                <li>The tracking number is entered correctly</li>
                                <li>The package was shipped within the last 90 days</li>
                                <li>You're using the correct tracking number format</li>
                            </ul>
                            <a href="contact.html" class="btn btn-primary">Contact Support</a>
                        </div>
                    </div>
                </div>
            `;
        } else {
            trackingResult.innerHTML = `
                <div class="tracking-info">
                    <h3>Tracking Information for: ${trackingNum}</h3>
                    <div class="package-summary">
                        <div class="summary-item">
                            <strong>Current Status:</strong>
                            <span class="status-${trackingData.status}">${getStatusText(trackingData.status)}</span>
                        </div>
                        <div class="summary-item">
                            <strong>Last Update:</strong> ${trackingData.timeline.find(item => item.status === 'active' || item.status === 'completed').time}
                        </div>
                    </div>
                    <div class="tracking-timeline">
                        ${trackingData.timeline.map(item => `
                            <div class="timeline-item ${item.status}">
                                <div class="timeline-icon">
                                    <i class="${item.icon}"></i>
                                </div>
                                <div class="timeline-content">
                                    <h4>${item.title}</h4>
                                    <p class="timeline-time">${item.time}</p>
                                    <p class="timeline-location">${item.location}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }
    }, 1500);
}

function getStatusText(status) {
    const statusMap = {
        'delivered': 'Delivered',
        'transit': 'In Transit',
        'out-for-delivery': 'Out for Delivery',
        'processing': 'Processing',
        'picked-up': 'Picked Up'
    };
    return statusMap[status] || 'Unknown';
}

// Contact form submission
function submitContactForm(event) {
    event.preventDefault();
    const form = event.target;
    
    if (validateForm(form)) {
        // Simulate form submission
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = 'Sending...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            alert('Thank you for your message! We will get back to you soon.');
            form.reset();
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 2000);
    }
}

// Quote calculator
function calculateQuote() {
    const weight = document.getElementById('weight');
    const destination = document.getElementById('destination');
    const service = document.getElementById('service');
    const quoteResult = document.getElementById('quoteResult');
    
    if (!weight.value || !destination.value || !service.value) {
        alert('Please fill in all fields');
        return;
    }
    
    // Simple quote calculation (replace with actual pricing logic)
    let baseRate = 50;
    let weightMultiplier = parseFloat(weight.value) * 10;
    let serviceMultiplier = service.value === 'express' ? 1.5 : 1;
    let destinationMultiplier = destination.value === 'international' ? 3 : 1;
    
    let totalCost = (baseRate + weightMultiplier) * serviceMultiplier * destinationMultiplier;
    
    quoteResult.innerHTML = `
        <div class="quote-result">
            <h3>Estimated Quote</h3>
            <div class="quote-details">
                <p><strong>Weight:</strong> ${weight.value} kg</p>
                <p><strong>Destination:</strong> ${destination.options[destination.selectedIndex].text}</p>
                <p><strong>Service:</strong> ${service.options[service.selectedIndex].text}</p>
                <div class="quote-total">
                    <strong>Estimated Cost: ₹${totalCost.toFixed(2)}</strong>
                </div>
            </div>
            <p class="quote-note">*This is an estimated quote. Final pricing may vary based on actual package dimensions and additional services.</p>
        </div>
    `;
    quoteResult.style.display = 'block';
}

// Add loading animation
function showLoading(element) {
    element.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Loading...</div>';
}

// FAQ functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        question.addEventListener('click', () => {
            const isActive = item.classList.contains('active');

            // Close all FAQ items
            faqItems.forEach(faqItem => {
                faqItem.classList.remove('active');
            });

            // Open clicked item if it wasn't active
            if (!isActive) {
                item.classList.add('active');
            }
        });
    });
}

// Mobile navigation enhancement
function initializeMobileNav() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');

    if (hamburger && navMenu) {
        // Create mobile menu overlay
        const overlay = document.createElement('div');
        overlay.className = 'nav-overlay';
        document.body.appendChild(overlay);

        hamburger.addEventListener('click', function() {
            const isActive = hamburger.classList.contains('active');

            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
            overlay.classList.toggle('active');
            document.body.classList.toggle('nav-open');
        });

        // Close menu when clicking overlay
        overlay.addEventListener('click', () => {
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');
            overlay.classList.remove('active');
            document.body.classList.remove('nav-open');
        });

        // Close mobile menu when clicking on a link
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                overlay.classList.remove('active');
                document.body.classList.remove('nav-open');
            });
        });
    }
}

// Enhanced form validation with real-time feedback
function enhancedValidateForm(form) {
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    let isValid = true;

    inputs.forEach(input => {
        const value = input.value.trim();
        const fieldName = input.name || input.id;
        let errorMessage = '';

        // Remove existing error styling
        input.classList.remove('error');
        const existingError = input.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Validate based on input type
        if (!value) {
            errorMessage = `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
            isValid = false;
        } else if (input.type === 'email' && !isValidEmail(value)) {
            errorMessage = 'Please enter a valid email address';
            isValid = false;
        } else if (input.type === 'tel' && !isValidPhone(value)) {
            errorMessage = 'Please enter a valid phone number';
            isValid = false;
        }

        // Show error if validation failed
        if (errorMessage) {
            input.classList.add('error');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = errorMessage;
            input.parentNode.appendChild(errorDiv);
        }
    });

    return isValid;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''));
}

// Initialize page-specific functionality
document.addEventListener('DOMContentLoaded', function() {
    // Set active navigation link based on current page
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });

    // Initialize FAQ functionality
    initializeFAQ();

    // Initialize enhanced mobile navigation
    initializeMobileNav();

    // Add real-time form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => {
                enhancedValidateForm(form);
            });
        });
    });

    // Add smooth scroll behavior to all internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add loading states to buttons
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.onclick) {
                this.classList.add('loading');
                setTimeout(() => {
                    this.classList.remove('loading');
                }, 2000);
            }
        });
    });
});
