/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    display: flex;
    align-items: center;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
}

.nav-logo img {
    height: 40px;
    margin-right: 10px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: #ffd700;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #ffd700;
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    color: white;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.3rem;
    color: #ffd700;
    margin-bottom: 1rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

.hero-image img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.features h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

/* Services Preview */
.services-preview {
    padding: 80px 0;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.services-preview h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: white;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
}

.service-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.service-content {
    padding: 1.5rem;
}

.service-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #333;
}

.service-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
}

.service-link:hover {
    color: #4facfe;
}

/* CTA Section */
.cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: white;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: white;
    opacity: 0.9;
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 50px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: #ffd700;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #667eea;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #ffd700;
    color: #333;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    opacity: 0.8;
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 120px 0 80px;
    color: white;
    text-align: center;
}

.page-header h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Services Detailed */
.services-detailed {
    padding: 80px 0;
    background: #f8f9fa;
}

.service-category {
    margin-bottom: 4rem;
}

.service-category h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.service-category h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

.service-card-detailed {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    margin-bottom: 2rem;
}

.service-card-detailed:hover {
    transform: translateY(-10px);
}

.service-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.service-info {
    padding: 2rem;
}

.service-info h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: #333;
}

.service-features {
    list-style: none;
    margin: 1.5rem 0;
}

.service-features li {
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.service-features i {
    color: #28a745;
    font-size: 0.9rem;
}

.service-pricing {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #eee;
}

.price {
    font-size: 1.3rem;
    font-weight: 600;
    color: #667eea;
}

/* Additional Services */
.additional-services {
    margin-top: 4rem;
    padding: 3rem 0;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 20px;
}

.additional-services h2 {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
}

.additional-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.additional-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.additional-card:hover {
    transform: translateY(-5px);
}

.additional-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.additional-card h3 {
    margin-bottom: 1rem;
    color: #333;
}

/* Quote Calculator */
.quote-calculator {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    margin-top: 4rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.quote-calculator h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.quote-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    align-items: end;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
}

.quote-result {
    margin-top: 2rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    border-left: 5px solid #667eea;
}

.quote-details {
    margin: 1rem 0;
}

.quote-total {
    font-size: 1.3rem;
    color: #667eea;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
}

.quote-note {
    font-size: 0.9rem;
    color: #666;
    font-style: italic;
    margin-top: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .quote-form {
        grid-template-columns: 1fr;
    }
}

/* About Page Styles */
.about-content {
    padding: 80px 0;
    background: #f8f9fa;
}

.about-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-bottom: 5rem;
}

.about-text h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.about-text h3 {
    font-size: 1.3rem;
    color: #667eea;
    margin-bottom: 2rem;
    font-weight: 500;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 1.5rem;
}

.company-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-item h4 {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

.about-image img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

/* Partnership Section */
.partnership-section {
    background: white;
    padding: 4rem;
    border-radius: 20px;
    margin-bottom: 5rem;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.partnership-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.partnership-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.partnership-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 2rem;
}

.partnership-benefits {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.benefit-item i {
    font-size: 2rem;
    color: #667eea;
    margin-top: 0.5rem;
}

.benefit-item h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.benefit-item p {
    color: #666;
    line-height: 1.6;
}

.partnership-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Values Section */
.values-section {
    margin-bottom: 5rem;
}

.values-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.value-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-10px);
}

.value-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.value-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.value-card p {
    color: #666;
    line-height: 1.6;
}

/* Why Choose Section */
.why-choose-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4rem;
    border-radius: 20px;
    margin-bottom: 5rem;
}

.why-choose-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: white;
    margin-bottom: 3rem;
}

.choose-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.choose-image img {
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.3);
}

.choose-list {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.choose-list li {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.choose-list i {
    font-size: 1.5rem;
    color: #ffd700;
    margin-top: 0.2rem;
}

.choose-list h4 {
    font-size: 1.2rem;
    color: white;
    margin-bottom: 0.5rem;
}

.choose-list p {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
}

/* Team/Commitment Section */
.team-section {
    background: white;
    padding: 4rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.team-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.commitment-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 2rem;
    text-align: center;
}

.commitment-image {
    text-align: center;
    margin-top: 3rem;
}

.commitment-image img {
    max-width: 500px;
    width: 100%;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

/* Mobile Responsive for About Page */
@media (max-width: 768px) {
    .about-section,
    .partnership-content,
    .choose-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .company-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .about-text h2 {
        font-size: 2rem;
    }

    .partnership-section,
    .why-choose-section,
    .team-section {
        padding: 2rem;
    }

    .values-grid {
        grid-template-columns: 1fr;
    }
}

/* Contact Page Styles */
.contact-content {
    padding: 80px 0;
    background: #f8f9fa;
}

.contact-info-section {
    margin-bottom: 5rem;
}

.contact-info-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 1rem;
}

.contact-info-section > p {
    text-align: center;
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 3rem;
}

.contact-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.contact-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-10px);
}

.contact-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.contact-details h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.contact-details p {
    color: #666;
    line-height: 1.6;
}

/* Contact Form Section */
.contact-form-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    margin-bottom: 5rem;
}

.form-container {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.form-container h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* Map Container */
.map-container {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    text-align: center;
}

.map-container h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.map-placeholder {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4rem 2rem;
    border-radius: 15px;
    color: white;
}

.map-placeholder i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.map-placeholder p {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.map-address {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 2rem;
}

/* Quick Services */
.quick-services {
    margin-bottom: 5rem;
}

.quick-services h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.quick-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.quick-service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.quick-service-card:hover {
    transform: translateY(-10px);
}

.quick-service-card i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.quick-service-card h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.quick-service-card p {
    color: #666;
    margin-bottom: 1.5rem;
}

.service-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: transform 0.3s ease;
}

.service-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* FAQ Section */
.faq-section {
    background: white;
    padding: 4rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.faq-section h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    border-bottom: 1px solid #eee;
    margin-bottom: 1rem;
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    cursor: pointer;
    transition: color 0.3s ease;
}

.faq-question:hover {
    color: #667eea;
}

.faq-question h3 {
    font-size: 1.2rem;
    color: inherit;
}

.faq-question i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding-bottom: 1.5rem;
    color: #666;
    line-height: 1.6;
}

/* Tracking Page Styles */
.tracking-content {
    padding: 80px 0;
    background: #f8f9fa;
}

.tracking-form-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-bottom: 5rem;
}

.tracking-form-container {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.tracking-form-container h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 2rem;
}

.tracking-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tracking-form input {
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 10px;
    font-size: 1.1rem;
    transition: border-color 0.3s ease;
}

.tracking-form input:focus {
    outline: none;
    border-color: #667eea;
}

.tracking-form small {
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.tracking-tips {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 2.5rem;
    border-radius: 20px;
    color: white;
}

.tracking-tips h3 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

.tracking-tips ul {
    list-style: none;
}

.tracking-tips li {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.tracking-tips i {
    color: #ffd700;
    margin-top: 0.2rem;
}

/* Tracking Results */
.tracking-result {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.tracking-info h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 2rem;
    text-align: center;
}

.tracking-status {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 10px;
    background: #f8f9fa;
    position: relative;
}

.status-item.completed {
    background: #d4edda;
    color: #155724;
}

.status-item.active {
    background: #fff3cd;
    color: #856404;
    border-left: 4px solid #ffc107;
}

.status-item i {
    font-size: 1.5rem;
}

.status-item span {
    font-weight: 500;
    flex: 1;
}

.status-item small {
    color: #666;
    font-size: 0.9rem;
}

/* Demo Section */
.demo-section {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-bottom: 5rem;
    text-align: center;
}

.demo-section h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1rem;
}

.demo-section p {
    color: #666;
    margin-bottom: 2rem;
}

.demo-numbers {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.demo-item {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
    text-align: center;
}

.demo-item:hover {
    transform: translateY(-5px);
}

.demo-item strong {
    display: block;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.demo-item span {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Tracking Features */
.tracking-features {
    margin-bottom: 5rem;
}

.tracking-features h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature-item {
    background: white;
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-10px);
}

.feature-item .feature-icon {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1.5rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 1rem;
}

.feature-item p {
    color: #666;
    line-height: 1.6;
}

/* Status Guide */
.status-guide {
    background: white;
    padding: 4rem;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-bottom: 5rem;
}

.status-guide h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 3rem;
}

.status-items {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 800px;
    margin: 0 auto;
}

.status-guide .status-item {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.status-guide .status-item:hover {
    transform: translateX(10px);
}

.status-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.status-icon.pickup {
    background: #6c757d;
}

.status-icon.processing {
    background: #fd7e14;
}

.status-icon.transit {
    background: #0d6efd;
}

.status-icon.delivery {
    background: #ffc107;
    color: #333;
}

.status-icon.delivered {
    background: #198754;
}

.status-info h3 {
    font-size: 1.3rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.status-info p {
    color: #666;
    line-height: 1.6;
}

/* Help Section */
.help-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 4rem;
    border-radius: 20px;
    color: white;
}

.help-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
}

.help-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.help-text p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.help-text ul {
    list-style: none;
}

.help-text li {
    margin-bottom: 0.5rem;
    padding-left: 1rem;
    position: relative;
}

.help-text li::before {
    content: '•';
    color: #ffd700;
    position: absolute;
    left: 0;
}

.help-contact h3 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: #ffd700;
}

.contact-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
}

.contact-option:hover {
    background: rgba(255,255,255,0.2);
}

.contact-option i {
    font-size: 1.2rem;
}

/* Mobile Responsive for Contact and Tracking */
@media (max-width: 768px) {
    .contact-form-section,
    .tracking-form-section,
    .help-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-cards,
    .quick-services-grid {
        grid-template-columns: 1fr;
    }

    .demo-numbers {
        flex-direction: column;
        align-items: center;
    }

    .status-guide .status-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Enhanced Mobile Navigation */
.nav-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.nav-overlay.active {
    opacity: 1;
    visibility: visible;
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        right: -100%;
        width: 80%;
        max-width: 300px;
        height: calc(100vh - 70px);
        background: white;
        flex-direction: column;
        padding: 2rem;
        box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        transition: right 0.3s ease;
        z-index: 1000;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-menu li {
        margin-bottom: 1rem;
    }

    .nav-link {
        color: #333;
        font-size: 1.1rem;
        padding: 0.5rem 0;
        border-bottom: 1px solid #eee;
    }

    .nav-link:hover,
    .nav-link.active {
        color: #667eea;
    }

    body.nav-open {
        overflow: hidden;
    }
}

/* Enhanced Form Styles */
.form-group input.error,
.form-group select.error,
.form-group textarea.error {
    border-color: #dc3545;
    background-color: #fff5f5;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: block;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    color: white;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #667eea;
    font-size: 1.1rem;
}

.loading i {
    margin-right: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Enhanced Tracking Styles */
.tracking-error {
    text-align: center;
    padding: 3rem;
    color: #dc3545;
}

.tracking-error i {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.tracking-error h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.error-help {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-top: 2rem;
    text-align: left;
}

.error-help p {
    color: #333;
    margin-bottom: 1rem;
}

.error-help ul {
    color: #666;
    margin-bottom: 2rem;
    padding-left: 1.5rem;
}

.package-summary {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.summary-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.status-delivered { color: #198754; }
.status-transit { color: #0d6efd; }
.status-out-for-delivery { color: #ffc107; }
.status-processing { color: #fd7e14; }
.status-picked-up { color: #6c757d; }

.tracking-timeline {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 10px;
    background: #f8f9fa;
    position: relative;
}

.timeline-item.completed {
    background: #d4edda;
    border-left: 4px solid #198754;
}

.timeline-item.active {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.2);
}

.timeline-item.pending {
    background: #f8f9fa;
    border-left: 4px solid #dee2e6;
    opacity: 0.7;
}

.timeline-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    background: #6c757d;
}

.timeline-item.completed .timeline-icon {
    background: #198754;
}

.timeline-item.active .timeline-icon {
    background: #ffc107;
    color: #333;
}

.timeline-content h4 {
    font-size: 1.2rem;
    color: #333;
    margin-bottom: 0.5rem;
}

.timeline-time {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 0.25rem;
}

.timeline-location {
    color: #666;
    font-size: 0.9rem;
}
